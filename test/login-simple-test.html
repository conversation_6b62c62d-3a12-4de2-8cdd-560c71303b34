<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kiểm tra đăng nhập đơn giản</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; }
        button { padding: 10px; margin: 5px; cursor: pointer; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .section { margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1>Kiểm tra đăng nhập đơn giản</h1>
    
    <div class="section">
        <h2>Phương pháp 1: Truyền trực tiếp</h2>
        <button onclick="testDirect()">Kiểm tra</button>
        <pre id="directResult">Kết quả sẽ hiển thị ở đây...</pre>
    </div>
    
    <div class="section">
        <h2>Phương pháp 2: Sử dụng biến</h2>
        <button onclick="testWithVariables()">Kiểm tra</button>
        <pre id="variablesResult">Kết quả sẽ hiển thị ở đây...</pre>
    </div>

    <script>
        // Phương pháp 1: Truyền trực tiếp
        async function testDirect() {
            const result = document.getElementById('directResult');
            result.textContent = 'Đang gửi yêu cầu...';
            
            const requestBody = {
                query: `
                    mutation {
                        auth_login(body: {
                            user_name: "admin",
                            password: "123456",
                            role_id: 1
                        }) {
                            id
                            full_name
                            avatar
                            role_id
                            created_at
                        }
                    }
                `
            };
            
            try {
                const response = await fetch('http://localhost:8003/graphql', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const data = await response.json();
                result.textContent = JSON.stringify(data, null, 2);
                console.log('Phản hồi (trực tiếp):', data);
            } catch (error) {
                result.textContent = 'Lỗi: ' + error.message;
                console.error('Lỗi (trực tiếp):', error);
            }
        }
        
        // Phương pháp 2: Sử dụng biến
        async function testWithVariables() {
            const result = document.getElementById('variablesResult');
            result.textContent = 'Đang gửi yêu cầu...';
            
            const requestBody = {
                query: `
                    mutation Auth_login($body: LoginInputDto!) {
                        auth_login(body: $body) {
                            id
                            full_name
                            avatar
                            role_id
                            created_at
                        }
                    }
                `,
                variables: {
                    body: {
                        user_name: "admin",
                        password: "123456",
                        role_id: 1
                    }
                }
            };
            
            try {
                const response = await fetch('http://localhost:8003/graphql', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const data = await response.json();
                result.textContent = JSON.stringify(data, null, 2);
                console.log('Phản hồi (biến):', data);
            } catch (error) {
                result.textContent = 'Lỗi: ' + error.message;
                console.error('Lỗi (biến):', error);
            }
        }
    </script>
</body>
</html>
