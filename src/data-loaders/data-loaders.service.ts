import { Injectable, Scope } from '@nestjs/common';
import * as DataLoader from 'dataloader';
import { AuthService } from '../modules/auth/services/auth.service';
import { In } from 'typeorm';

@Injectable({ scope: Scope.REQUEST })
export class DataLoaderService {
    private loaders = new Map<string, DataLoader<number, any>>();

    constructor(private readonly authService: AuthService) {}

    /**
     * ✅ Hàm tạo key duy nhất cho mỗi entity và relation
     */
    private getLoaderKey(entity: Function, relation?: string): string {
        return `${entity.name}_${relation || 'default'}`;
    }

    /**
     * ✅ Batch Load for One-to-One hoặc Many-to-One
     */
    public relationBatchOne<T>(entity: new () => T, relation?: string): DataLoader<number, T | null> {
        const key = this.getLoaderKey(entity);
        if (!this.loaders.has(key)) {
            const loader = new DataLoader<number, T | null>(async (itemIds: number[]) => {
                const repo = this.authService.repo.manager.getRepository(entity);
                if (relation) {
                    const items = await repo.find({
                        where: { [relation]: { id: In(itemIds) } },
                        relations: [relation],
                    });
                    const itemMap = new Map<number, T>();
                    for (const item of items) {
                        const relationId = (item as any)[relation]?.id;
                        if (relationId) {
                            itemMap.set(relationId, item as T);
                        }
                    }
                    return itemIds.map((id) => itemMap.get(id) || null);
                } else {
                    const items = await repo.find({ where: { id: In(itemIds) } });
                    const itemMap = new Map<number, T>();
                    for (const item of items) {
                        itemMap.set((item as any).id, item as T);
                    }
                    return itemIds.map((id) => itemMap.get(id) || null);
                }
            });
            this.loaders.set(key, loader);
        }
        return this.loaders.get(key)!;
    }

    /**
     * ✅ Batch Load for One-to-Many
     */
    public relationBatchOneMany<T>(entity: new () => T, relation: string): DataLoader<number, T[]> {
        const key = this.getLoaderKey(entity, relation);
        if (!this.loaders.has(key)) {
            const loader = new DataLoader<number, T[]>(async (itemIds: number[]) => {
                const repo = this.authService.repo.manager.getRepository(entity);
                // Use a more explicit query to debug
                const relationItems = await repo
                    .createQueryBuilder('entity')
                    .where(`entity.${relation}_id IN (:...ids)`, { ids: itemIds })
                    .getMany();
                const itemMap = new Map<number, T[]>();
                for (const id of itemIds) {
                    itemMap.set(id, []);
                }
                for (const item of relationItems) {
                    const relationId = (item as any)[`${relation}_id`];
                    if (relationId && itemMap.has(relationId)) {
                        itemMap.get(relationId)?.push(item as T);
                    }
                }

                return itemIds.map((id) => itemMap.get(id) || []);
            });
            this.loaders.set(key, loader);
        }
        return this.loaders.get(key) as DataLoader<number, T[]>;
    }

    /**
     * ✅ Batch Load for Many-to-Many
     */
    public relationBatchManyMany<T>(entity: new () => T, relation: string): DataLoader<number, T[]> {
        const key = this.getLoaderKey(entity, relation);
        if (!this.loaders.has(key)) {
            const loader = new DataLoader<number, T[]>(async (itemIds: number[]) => {
                const repo = this.authService.repo.manager.getRepository(entity);
                const relationItems = await repo
                    .createQueryBuilder('entity')
                    .leftJoinAndSelect(`entity.${relation}`, 'relation')
                    .where(`relation.id IN (:...ids)`, { ids: itemIds })
                    .getMany();

                const itemMap = new Map<number, T[]>();
                for (const item of relationItems) {
                    for (const rel of (item as any)[relation] || []) {
                        if (!itemMap.has(rel.id)) {
                            itemMap.set(rel.id, []);
                        }
                        itemMap.get(rel.id)?.push(item as T);
                    }
                }
                return itemIds.map((id) => itemMap.get(id) || []);
            });
            this.loaders.set(key, loader);
        }
        return this.loaders.get(key)!;
    }
}
