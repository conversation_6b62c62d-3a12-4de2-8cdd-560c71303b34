import { Column, <PERSON><PERSON><PERSON>, <PERSON>in<PERSON><PERSON>um<PERSON>, ManyToOne, OneToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { UserEntity } from './user.entity';
import { pathToUrl } from '../commons/helpers/file.helper';
import { BaseEntity } from '../commons/bases/base.entity';
import { TemplateEntity } from './template.entity';
import { FolderEntity } from './folder.entity';
import { DepartmentEntity } from './department.entity';

@ObjectType('files')
@Entity('files')
export class FileEntity extends BaseEntity {
    @Column()
    @Field()
    file_name: string;

    @Column({
        transformer: {
            to(value) {
                return value.toString();
            },
            from(value) {
                return value ? pathToUrl(value) : value;
            },
        },
    })
    @Field()
    file_url: string;

    @Column({ type: 'integer' })
    @Field()
    file_size: number;

    @Column()
    @Field()
    mime_type: string;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    folder_id?: number;

    @Column()
    @Field(() => Int)
    department_id: number;

    @ManyToOne(() => FolderEntity, (folder) => folder.files)
    @JoinColumn({ name: 'folder_id' })
    @Field(() => FolderEntity, { nullable: true })
    folder?: FolderEntity;

    @OneToOne(() => UserEntity, (u) => u.avatar)
    // @Field(() => UserEntity, { nullable: true })
    user?: UserEntity;

    @OneToOne(() => TemplateEntity, (t) => t.avatar)
    // @Field(() => TemplateEntity, { nullable: true })
    template?: TemplateEntity;

    @ManyToOne(() => DepartmentEntity, (d) => d.files)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;
}
