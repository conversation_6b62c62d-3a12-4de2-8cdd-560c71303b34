import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ArticleEntity } from './article.entity';
import { FileEntity } from './file.entity';

@ObjectType('article_files')
@Entity('article_files')
export class ArticleFileEntity extends BaseEntity {
    @Column()
    @Field(() => Int)
    article_id: number;

    @Column()
    @Field(() => Int)
    file_id: number;

    @ManyToOne(() => ArticleEntity, (article) => article.articleFiles)
    @JoinColumn({ name: 'article_id' })
    @Field(() => ArticleEntity)
    article: ArticleEntity;

    @ManyToOne(() => FileEntity)
    @JoinColumn({ name: 'file_id' })
    @Field(() => FileEntity)
    file: FileEntity;
}
