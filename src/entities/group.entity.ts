import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToMany, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ActionEntity } from './action.entity';
import { UserEntity } from './user.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { UserDepartmentEntity } from './user-department.entity';
import { DepartmentEntity } from './department.entity';

@ObjectType('groups')
@Entity('groups')
@SearchFields(['name'])
export class GroupEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column()
    @Field(() => Int)
    department_id: number;

    @ManyToMany(() => ActionEntity, (action) => action.groups)
    @Field(() => [ActionEntity], { nullable: true })
    actions?: ActionEntity[];

    @ManyToMany(() => UserEntity, (user) => user.groups)
    @Field(() => [UserEntity], { nullable: true })
    users?: UserEntity[];

    @ManyToOne(() => DepartmentEntity, (d) => d.groups)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    @ManyToMany(() => UserDepartmentEntity, (ud) => ud.groups)
    @Field(() => [UserDepartmentEntity], { nullable: true })
    userDepartments?: UserDepartmentEntity[];
}
