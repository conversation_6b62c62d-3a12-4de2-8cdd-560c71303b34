import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ArticleEntity } from './article.entity';
import { CategoryEntity } from './category.entity';

@ObjectType('article_categories')
@Entity('article_categories')
export class ArticleCategoryEntity extends BaseEntity {
    @Column()
    @Field(() => Int)
    article_id: number;

    @Column()
    @Field(() => Int)
    category_id: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    display_order: number;

    @ManyToOne(() => ArticleEntity, (article) => article.articleCategories)
    @JoinColumn({ name: 'article_id' })
    @Field(() => ArticleEntity)
    article: ArticleEntity;

    @ManyToOne(() => CategoryEntity)
    @JoinColumn({ name: 'category_id' })
    @Field(() => CategoryEntity)
    category: CategoryEntity;
}
