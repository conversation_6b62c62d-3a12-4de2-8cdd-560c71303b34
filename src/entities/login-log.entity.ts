import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { Field, GraphQLISODateTime, Int, ObjectType } from '@nestjs/graphql';
import { BaseIdEntity } from '../commons/bases/base-id.entity';
import { UserEntity } from './user.entity';

@Entity('login_logs')
@ObjectType('login_logs')
export class LoginLogEntity extends BaseIdEntity {
    @Column()
    @Field(() => Int)
    user_id: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    ip_address?: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    user_agent?: string;

    @Column({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
    @Field(() => GraphQLISODateTime)
    created_at: Date;

    @ManyToOne(() => UserEntity, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'user_id' })
    @Field(() => UserEntity)
    user: UserEntity;
}
