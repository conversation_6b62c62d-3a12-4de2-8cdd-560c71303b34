import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { DepartmentEntity } from './department.entity';
import { FileEntity } from './file.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

@Entity('folders')
@ObjectType('folders')
@SearchFields(['name'])
export class FolderEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    parent_id?: number;

    @Column()
    @Field(() => Int)
    department_id: number;

    // Self-referencing relationship for parent folder
    @ManyToOne(() => FolderEntity, (folder) => folder.children, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'parent_id' })
    @Field(() => FolderEntity, { nullable: true })
    parent?: FolderEntity;

    // Children folders
    @OneToMany(() => FolderEntity, (folder) => folder.parent)
    @Field(() => [FolderEntity], { nullable: true })
    children?: FolderEntity[];

    // Department relationship
    @ManyToOne(() => DepartmentEntity, (d) => d.folders)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    // Files in this folder
    @OneToMany(() => FileEntity, (file) => file.folder)
    @Field(() => [FileEntity], { nullable: true })
    files?: FileEntity[];
}
