import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { DepartmentEntity } from './department.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

@ObjectType('configs')
@Entity('configs')
@SearchFields(['code', 'name'])
export class ConfigEntity extends BaseEntity {
    @Column({ unique: false })
    @Field()
    code: string;

    @Column()
    @Field()
    name: string;

    @Column()
    @Field()
    content: string;

    @Column()
    @Field(() => Int)
    department_id: number;

    @ManyToOne(() => DepartmentEntity, (d) => d.configs)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;
}
