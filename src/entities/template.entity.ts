import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { DepartmentEntity } from './department.entity';
import { FileEntity } from './file.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

@ObjectType('templates')
@Entity('templates')
@SearchFields(['name'])
export class TemplateEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column({ type: 'text' })
    @Field()
    content: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    avatar_id?: number;

    @Column()
    @Field(() => Int)
    department_id: number;

    @OneToOne(() => FileEntity)
    @JoinColumn({ name: 'avatar_id' })
    @Field(() => FileEntity, { nullable: true })
    avatar?: FileEntity;

    @ManyToOne(() => DepartmentEntity, (d) => d.templates)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;
}
