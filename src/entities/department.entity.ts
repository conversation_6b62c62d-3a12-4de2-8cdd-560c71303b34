import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { ItemStatus } from '../commons/enums.common';
import { UserDepartmentEntity } from './user-department.entity';
import { GroupEntity } from './group.entity';
import { WorkflowEntity } from './workflow.entity';
import { WorkflowPermissionEntity } from './workflow-permission.entity';
import { CategoryEntity } from './category.entity';
import { TagEntity } from './tag.entity';
import { PortletEntity } from './portlet.entity';
import { TemplateEntity } from './template.entity';
import { AdvertiseEntity } from './advertise.entity';
import { LayoutEntity } from './layout.entity';
import { ConfigEntity } from './config.entity';
import { AdvItemEntity } from './adv-item.entity';
import { FolderEntity } from './folder.entity';
import { FileEntity } from './file.entity';

@ObjectType('departments')
@Entity('departments')
@SearchFields(['name'])
export class DepartmentEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    parent_id: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    desc: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    display_order: number;

    @Column({ type: 'smallint', nullable: true })
    @Field(() => Int, { nullable: true })
    language_id?: number;

    @OneToMany(() => DepartmentEntity, (d) => d.parent)
    @Field(() => [DepartmentEntity], { nullable: true })
    children?: DepartmentEntity[];

    @ManyToOne(() => DepartmentEntity, (d) => d.children)
    @JoinColumn({ name: 'parent_id' })
    @Field(() => DepartmentEntity, { nullable: true })
    parent?: DepartmentEntity;

    @OneToMany(() => GroupEntity, (g) => g.department)
    @Field(() => [GroupEntity], { nullable: true })
    groups?: GroupEntity[];

    @OneToMany(() => UserDepartmentEntity, (ud) => ud.department)
    @Field(() => [UserDepartmentEntity], { nullable: true })
    userDepartments?: UserDepartmentEntity[];

    @OneToMany(() => WorkflowEntity, (w) => w.department)
    @Field(() => [WorkflowEntity], { nullable: true })
    workflows?: WorkflowEntity[];

    @OneToMany(() => WorkflowPermissionEntity, (w) => w.department)
    @Field(() => [WorkflowPermissionEntity], { nullable: true })
    workflowPermissions?: WorkflowPermissionEntity[];

    @OneToMany(() => CategoryEntity, (c) => c.department)
    @Field(() => [CategoryEntity], { nullable: true })
    categories?: CategoryEntity[];

    @OneToMany(() => TagEntity, (t) => t.department)
    @Field(() => [TagEntity], { nullable: true })
    tags?: TagEntity[];

    @OneToMany(() => PortletEntity, (p) => p.department)
    @Field(() => [PortletEntity], { nullable: true })
    portlets?: PortletEntity[];

    @OneToMany(() => TemplateEntity, (t) => t.department)
    @Field(() => [TemplateEntity], { nullable: true })
    templates?: TemplateEntity[];

    @OneToMany(() => LayoutEntity, (l) => l.department)
    @Field(() => [LayoutEntity], { nullable: true })
    layouts?: LayoutEntity[];

    @OneToMany(() => AdvertiseEntity, (a) => a.department)
    @Field(() => [AdvertiseEntity], { nullable: true })
    advertises?: AdvertiseEntity[];

    @OneToMany(() => AdvItemEntity, (ai) => ai.department)
    @Field(() => [AdvItemEntity], { nullable: true })
    advItems?: AdvItemEntity[];

    @OneToMany(() => ConfigEntity, (c) => c.department)
    @Field(() => [ConfigEntity], { nullable: true })
    configs?: ConfigEntity[];

    @OneToMany(() => FolderEntity, (f) => f.department)
    @Field(() => [FolderEntity], { nullable: true })
    folders?: FolderEntity[];

    @OneToMany(() => FileEntity, (f) => f.department)
    @Field(() => [FileEntity], { nullable: true })
    files?: FileEntity[];
}
