import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { Field, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { CategoryEntity } from './category.entity';
import { DepartmentEntity } from './department.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

@ObjectType('layouts')
@Entity('layouts')
@SearchFields(['name'])
export class LayoutEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column({ type: 'text' })
    @Field()
    content: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    layout_type_id: LayoutType;

    @Column()
    @Field(() => Int)
    department_id: number;

    @ManyToOne(() => DepartmentEntity, (d) => d.layouts)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    @OneToMany(() => CategoryEntity, (category) => category.layout)
    @Field(() => [CategoryEntity], { nullable: true })
    categories?: CategoryEntity[];
}

export enum LayoutType {
    ARTICLE = 1,
    CATEGORY = 2,
}

registerEnumType(LayoutType, {
    name: 'LayoutType',
});
