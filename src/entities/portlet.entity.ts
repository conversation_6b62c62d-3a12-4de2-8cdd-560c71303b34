import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { DepartmentEntity } from './department.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

@ObjectType('portlets')
@Entity('portlets')
@SearchFields(['code', 'name'])
export class PortletEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column()
    @Field()
    code: string;

    @Column()
    @Field()
    sql: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column({ type: 'text' })
    @Field()
    content: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    layout_type_id: PortletType;

    @Column()
    @Field(() => Int)
    department_id: number;

    @ManyToOne(() => DepartmentEntity, (d) => d.portlets)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;
}

export enum PortletType {
    ARTICLE = 1,
    CATEGORY = 2,
}

registerEnumType(PortletType, {
    name: 'PortletType',
});
