import { Column, <PERSON><PERSON><PERSON>, <PERSON>in<PERSON><PERSON><PERSON><PERSON>, ManyToMany, ManyToOne, OneToMany } from 'typeorm';
import { Field, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { ArticleTypes } from './workflow-permission-article-type.entity';
import { DepartmentEntity } from './department.entity';
import { LayoutEntity } from './layout.entity';
import { AdvertiseEntity } from './advertise.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

@ObjectType('categories')
@Entity('categories')
@SearchFields(['name'])
export class CategoryEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column({ nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    article_type_id: ArticleTypes;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    category_type_id: CategoryTypes;

    @Column()
    @Field(() => Int)
    department_id: number;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    parent_id?: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    display_order: number;

    @Column()
    @Field()
    slug: string;

    @Column({ type: 'int', nullable: true })
    @Field(() => Int, { nullable: true })
    layout_id?: number;

    @ManyToOne(() => LayoutEntity, (l) => l.categories)
    @JoinColumn({ name: 'layout_id' })
    @Field(() => LayoutEntity, { nullable: true })
    layout?: LayoutEntity;

    @Column({ type: 'boolean', default: true })
    @Field(() => Boolean)
    is_major: boolean;

    @ManyToOne(() => DepartmentEntity, (d) => d.categories)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    @OneToMany(() => CategoryEntity, (c) => c.parent)
    @Field(() => [CategoryEntity], { nullable: true })
    children?: CategoryEntity[];

    @ManyToOne(() => CategoryEntity, (c) => c.children)
    @JoinColumn({ name: 'parent_id' })
    @Field(() => CategoryEntity, { nullable: true })
    parent?: CategoryEntity;

    @ManyToMany(() => AdvertiseEntity, (advertise) => advertise.categories)
    @Field(() => [AdvertiseEntity], { nullable: true })
    advertises?: AdvertiseEntity[];
}

export enum CategoryTypes {
    TOPIC = 1,
    CATEGORY = 2,
}

export enum Languages {
    VI = 1,
    EN = 2,
    CN = 3,
}

registerEnumType(CategoryTypes, {
    name: 'CategoryTypes',
});

registerEnumType(Languages, {
    name: 'Languages',
});
