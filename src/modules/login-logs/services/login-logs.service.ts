import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseService } from '../../../commons/bases/base.service';
import { LoginLogEntity } from '../../../entities/login-log.entity';
import { CreateLoginLogDto } from '../dtos/create-login-log.dto';

@Injectable()
export class LoginLogsService extends BaseService<LoginLogEntity> {
    constructor(
        @InjectRepository(LoginLogEntity)
        public readonly repo: Repository<LoginLogEntity>
    ) {
        super(repo);
    }

    async createLoginLog(data: CreateLoginLogDto): Promise<LoginLogEntity> {
        const loginLog = this.repo.create({
            user_id: data.user_id,
            ip_address: data.ip_address,
            user_agent: data.user_agent,
            created_at: new Date(),
        });

        return this.repo.save(loginLog);
    }
}
