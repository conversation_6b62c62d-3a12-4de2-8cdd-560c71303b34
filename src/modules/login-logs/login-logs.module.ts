import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LoginLogEntity } from '../../entities/login-log.entity';
import { LoginLogsService } from './services/login-logs.service';
import { LoginLogsResolver } from './login-logs.resolver';
import { DataLoaderModule } from '../../data-loaders/data-loaders.module';

@Module({
    imports: [TypeOrmModule.forFeature([LoginLogEntity]), DataLoaderModule],
    providers: [LoginLogsService, LoginLogsResolver],
    exports: [LoginLogsService],
})
export class LoginLogsModule {}
