import { <PERSON>rgs, Int, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { LoginLogsService } from './services/login-logs.service';
import { LoginLogEntity } from '../../entities/login-log.entity';
import { AuthUser } from '../auth/auth.decorator';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { LoginLogsModel } from './models/login-logs.model';
import { IPaginatedType } from '../../commons/bases/base.model';
import { DataLoaderService } from '../../data-loaders/data-loaders.service';
import { UserEntity } from '../../entities/user.entity';
import { AuthResolver } from '../../commons/decorators/graphql.decorators';

@AuthResolver(LoginLogEntity)
export class LoginLogsResolver {
    constructor(
        private readonly loginLogsService: LoginLogsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => UserEntity, { nullable: true })
    async user(@Parent() parent: LoginLogEntity): Promise<UserEntity | null> {
        if (!parent.user_id) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.user_id);
    }

    @Query(() => LoginLogsModel, { name: 'login_logs_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<LoginLogEntity>> {
        return this.loginLogsService.search(body);
    }
}
