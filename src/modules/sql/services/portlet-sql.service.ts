import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PortletEntity } from '../../../entities/portlet.entity';
import { SqlExecutionService } from './sql-execution.service';

@Injectable()
export class PortletSqlService {
    constructor(
        @InjectRepository(PortletEntity)
        private readonly portletRepository: Repository<PortletEntity>,
        private readonly sqlExecutionService: SqlExecutionService
    ) {}

    /**
     * Finds a portlet by code and department_id and executes its SQL
     * @param code The portlet code
     * @param departmentId The department ID
     * @returns The query results
     */
    async executePortletSql(code: string, departmentId: number): Promise<any> {
        // Find the portlet
        const portlet = await this.portletRepository.findOne({
            where: {
                code,
                department_id: departmentId,
            },
        });

        if (!portlet) {
            throw new NotFoundException(`Portlet with code ${code} and department_id ${departmentId} not found`);
        }

        // Execute the portlet's SQL
        return this.sqlExecutionService.executeSql(portlet.sql);
    }
}
