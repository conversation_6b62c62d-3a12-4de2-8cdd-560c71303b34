import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { SqlValidationService } from './sql-validation.service';

@Injectable()
export class SqlExecutionService {
    constructor(
        private readonly dataSource: DataSource,
        private readonly sqlValidationService: SqlValidationService
    ) {}

    /**
     * Executes a SQL query after validating it
     * @param sql The SQL query to execute
     * @returns The query results
     */
    async executeSql(sql: string): Promise<any> {
        // Validate the SQL query
        const validationResult = this.sqlValidationService.validateSql(sql);
        if (!validationResult.isValid) {
            throw new BadRequestException(validationResult.error);
        }

        try {
            // Execute the SQL query
            return this.dataSource.query(sql);
        } catch (error) {
            throw new InternalServerErrorException(`Error executing SQL query: ${error.message}`);
        }
    }
}
