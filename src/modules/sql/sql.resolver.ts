import { Args, Query } from '@nestjs/graphql';
import { AuthResolver } from '../../commons/decorators/graphql.decorators';
import { SqlExecutionService } from './services/sql-execution.service';
import { PortletSqlService } from './services/portlet-sql.service';
import { ExecuteSqlInputDto } from './dtos/execute-sql-input.dto';
import { ExecutePortletSqlInputDto } from './dtos/execute-portlet-sql-input.dto';
import GraphQLJSON from 'graphql-type-json';

@AuthResolver()
export class SqlResolver {
    constructor(
        private readonly sqlExecutionService: SqlExecutionService,
        private readonly portletSqlService: PortletSqlService
    ) {}

    /**
     * API endpoint that accepts a SQL string, validates it, and returns the query results
     * @param body The SQL query input
     * @returns The query results
     */
    @Query(() => GraphQLJSON, { name: 'execute_sql' })
    async executeSql(@Args('body') body: ExecuteSqlInputDto): Promise<object[]> {
        return this.sqlExecutionService.executeSql(body.sql);
    }

    /**
     * API endpoint that accepts code and department_id, finds the corresponding portlet,
     * extracts its SQL, validates it, and returns the query results
     * @param body The portlet SQL input
     * @returns The query results
     */
    @Query(() => GraphQLJSON, { name: 'execute_portlet_sql' })
    async executePortletSql(@Args('body') body: ExecutePortletSqlInputDto): Promise<object[]> {
        return this.portletSqlService.executePortletSql(body.code, body.department_id);
    }
}
