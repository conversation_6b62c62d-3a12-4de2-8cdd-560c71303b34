import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { DepartmentEntity } from '../../../entities/department.entity';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { DepartmentsService } from '../services/departments.service';
import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { GroupEntity } from '../../../entities/group.entity';
import { UserDepartmentEntity } from '../../../entities/user-department.entity';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { DepartmentsModel } from '../models/departments.model';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { DepartmentSaveInputDto } from '../dtos/department-save-input.dto';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { WorkflowEntity } from '../../../entities/workflow.entity';
import { WorkflowPermissionEntity } from '../../../entities/workflow-permission.entity';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { CategoryEntity } from '../../../entities/category.entity';
import { TagEntity } from '../../../entities/tag.entity';
import { PortletEntity } from '../../../entities/portlet.entity';
import { TemplateEntity } from '../../../entities/template.entity';
import { ConfigEntity } from '../../../entities/config.entity';
import { AdvItemEntity } from '../../../entities/adv-item.entity';
import { FolderEntity } from '../../../entities/folder.entity';

@AuthResolver(DepartmentEntity)
export class DepartmentsResolver {
    constructor(
        private readonly dataLoader: DataLoaderService,
        private readonly departmentsService: DepartmentsService
    ) {}

    @ResolveField(() => [GroupEntity], { nullable: true })
    async groups(@Parent() parent: DepartmentEntity): Promise<GroupEntity[]> {
        return this.dataLoader.relationBatchOneMany(GroupEntity, 'department').load(parent.id);
    }

    @ResolveField(() => [UserDepartmentEntity], { nullable: true })
    async userDepartments(@Parent() parent: DepartmentEntity): Promise<UserDepartmentEntity[]> {
        return this.dataLoader.relationBatchOneMany(UserDepartmentEntity, 'department').load(parent.id);
    }

    @ResolveField(() => [DepartmentEntity], { nullable: true })
    async children(@Parent() parent: DepartmentEntity): Promise<DepartmentEntity[]> {
        return this.dataLoader.relationBatchOneMany(DepartmentEntity, 'parent').load(parent.id);
    }

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async parent(@Parent() parent: DepartmentEntity): Promise<DepartmentEntity | null> {
        if (!parent.parent_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.id);
    }

    @ResolveField(() => [WorkflowEntity], { nullable: true })
    async workflows(@Parent() parent: DepartmentEntity): Promise<WorkflowEntity[]> {
        return this.dataLoader.relationBatchOneMany(WorkflowEntity, 'department').load(parent.id);
    }

    @ResolveField(() => [WorkflowPermissionEntity], { nullable: true })
    async workflowPermissions(@Parent() parent: DepartmentEntity): Promise<WorkflowPermissionEntity[]> {
        return this.dataLoader.relationBatchOneMany(WorkflowPermissionEntity, 'department').load(parent.id);
    }

    @ResolveField(() => [CategoryEntity], { nullable: true })
    async categories(@Parent() parent: DepartmentEntity): Promise<CategoryEntity[]> {
        return this.dataLoader.relationBatchOneMany(CategoryEntity, 'department').load(parent.id);
    }

    @ResolveField(() => [TagEntity], { nullable: true })
    async tags(@Parent() parent: DepartmentEntity): Promise<TagEntity[]> {
        return this.dataLoader.relationBatchOneMany(TagEntity, 'department').load(parent.id);
    }

    @ResolveField(() => [PortletEntity], { nullable: true })
    async portlets(@Parent() parent: DepartmentEntity): Promise<PortletEntity[]> {
        return this.dataLoader.relationBatchOneMany(PortletEntity, 'department').load(parent.id);
    }

    @ResolveField(() => [TemplateEntity], { nullable: true })
    async templates(@Parent() parent: DepartmentEntity): Promise<TemplateEntity[]> {
        return this.dataLoader.relationBatchOneMany(TemplateEntity, 'department').load(parent.id);
    }

    @ResolveField(() => [AdvItemEntity], { nullable: true })
    async advItems(@Parent() parent: DepartmentEntity): Promise<AdvItemEntity[]> {
        return this.dataLoader.relationBatchOneMany(AdvItemEntity, 'department').load(parent.id);
    }

    @ResolveField(() => [ConfigEntity], { nullable: true })
    async configs(@Parent() parent: DepartmentEntity): Promise<ConfigEntity[]> {
        return this.dataLoader.relationBatchOneMany(ConfigEntity, 'department').load(parent.id);
    }

    @ResolveField(() => [FolderEntity], { nullable: true })
    async folders(@Parent() parent: DepartmentEntity): Promise<FolderEntity[]> {
        return this.dataLoader.relationBatchOneMany(FolderEntity, 'department').load(parent.id);
    }

    @Query(() => DepartmentsModel, { name: 'departments_list' })
    async list(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<DepartmentEntity>> {
        return this.departmentsService.search(body);
    }

    @Query(() => DepartmentEntity, { name: 'departments_detail' })
    async view(@Args('id', { type: () => Number }) id: number): Promise<DepartmentEntity> {
        return this.departmentsService.findOne(id).then((res) => {
            if (!res) {
                throw new NotFoundException();
            }
            return res;
        });
    }

    @Mutation(() => DepartmentEntity, { name: 'departments_create' })
    async store(@Args('body') body: DepartmentSaveInputDto, @AuthUser() auth: UserEntity): Promise<DepartmentEntity> {
        return this.departmentsService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => DepartmentEntity, { name: 'departments_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: DepartmentSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<DepartmentEntity> {
        return this.departmentsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'departments_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<Boolean> {
        const userDepartments = await this.departmentsService.repo.manager.find(UserDepartmentEntity, {
            where: { department_id: id },
        });
        if (userDepartments.length) {
            throw new BadRequestException();
        }
        await this.departmentsService.softDelete(id, auth.id);
        return true;
    }
}
