import { Field, InputType, Int } from '@nestjs/graphql';
import { IsBoolean, IsEnum, IsInt, IsNotEmpty, IsOptional, IsPositive, IsString } from 'class-validator';
import { groupValidationMessages, validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { IsUnique } from '../../../commons/validators/is-unique.validator';
import { GroupEntity } from '../../../entities/group.entity';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { DepartmentEntity } from '../../../entities/department.entity';
import { ItemStatus } from '../../../commons/enums.common';
import { ArticleTypes } from '../../../entities/workflow-permission-article-type.entity';
import { CategoryEntity, CategoryTypes, Languages } from '../../../entities/category.entity';
import { LayoutEntity } from '../../../entities/layout.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class CategorySaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @IsUnique(GroupEntity, { message: groupValidationMessages.IS_EXISTS })
    name: string;

    @Field(() => Int)
    @IsEnum(ItemStatus)
    status_id: ItemStatus;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    desc?: string;

    @Field(() => Int)
    @IsEnum(ArticleTypes)
    article_type_id: ArticleTypes;

    @Field(() => Int)
    @IsEnum(CategoryTypes)
    category_type_id: CategoryTypes;

    @Field(() => Int)
    @IsNotEmpty()
    @IdExists(DepartmentEntity)
    department_id: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(CategoryEntity)
    parent_id: number;

    @Field(() => Int)
    @IsNotEmpty()
    @IsInt()
    @IsPositive()
    display_order: number;

    @Field()
    @IsNotEmpty()
    @IsString()
    slug: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(LayoutEntity)
    layout_id?: number;

    @Field(() => Boolean)
    @IsBoolean()
    is_major: boolean;
}
