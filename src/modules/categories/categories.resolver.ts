import { AuthResolver } from '../../commons/decorators/graphql.decorators';
import { CategoryEntity } from '../../entities/category.entity';
import { CategoriesService } from './categories.service';
import { DataLoaderService } from '../../data-loaders/data-loaders.service';
import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { DepartmentEntity } from '../../entities/department.entity';
import { LayoutEntity } from '../../entities/layout.entity';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { CategoriesModel } from './models/categories.model';
import { IPaginatedType } from '../../commons/bases/base.model';
import { NotFoundException } from '@nestjs/common';
import { AuthUser } from '../auth/auth.decorator';
import { UserEntity } from '../../entities/user.entity';
import { CategorySaveInputDto } from './dtos/category-save-input.dto';
import { AdvertiseEntity } from '../../entities/advertise.entity';

@AuthResolver(CategoryEntity)
export class CategoriesResolver {
    constructor(
        private readonly categoriesService: CategoriesService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: CategoryEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @ResolveField(() => [CategoryEntity], { nullable: true })
    async children(@Parent() parent: CategoryEntity): Promise<CategoryEntity[]> {
        return this.dataLoader.relationBatchOneMany(CategoryEntity, 'parent').load(parent.id);
    }

    @ResolveField(() => CategoryEntity, { nullable: true })
    async parent(@Parent() parent: CategoryEntity): Promise<CategoryEntity | null> {
        if (!parent.parent_id) return null;
        return this.dataLoader.relationBatchOne(CategoryEntity).load(parent.parent_id);
    }

    @ResolveField(() => LayoutEntity, { nullable: true })
    async layout(@Parent() parent: CategoryEntity): Promise<LayoutEntity | null> {
        if (!parent.layout_id) return null;
        return this.dataLoader.relationBatchOne(LayoutEntity).load(parent.layout_id);
    }

    @ResolveField(() => [AdvertiseEntity], { nullable: true })
    async advertises(@Parent() parent: CategoryEntity): Promise<AdvertiseEntity[]> {
        return this.dataLoader.relationBatchManyMany(AdvertiseEntity, 'categories').load(parent.id);
    }

    @Query(() => CategoriesModel, { name: 'categories_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<CategoryEntity>> {
        return this.categoriesService.search(body);
    }

    @Query(() => CategoryEntity, { name: 'categories_detail' })
    async view(@Args('id', { type: () => Number }) id: number): Promise<CategoryEntity> {
        return this.categoriesService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => CategoryEntity, { name: 'categories_create' })
    async store(@Args('body') body: CategorySaveInputDto, @AuthUser() auth: UserEntity): Promise<CategoryEntity> {
        return this.categoriesService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => CategoryEntity, { name: 'categories_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: CategorySaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<CategoryEntity> {
        return this.categoriesService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'categories_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<Boolean> {
        await this.categoriesService.softDelete(id, auth.id);
        return true;
    }
}
