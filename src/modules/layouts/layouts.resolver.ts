import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { AuthResolver } from '../../commons/decorators/graphql.decorators';
import { DataLoaderService } from '../../data-loaders/data-loaders.service';
import { LayoutEntity } from '../../entities/layout.entity';
import { LayoutsService } from './layouts.service';
import { LayoutSaveInputDto } from './dtos/layout-save-input.dto';
import { LayoutsModel } from './models/layouts.model';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { UserEntity } from '../../entities/user.entity';
import { IPaginatedType } from '../../commons/bases/base.model';
import { AuthUser } from '../auth/auth.decorator';
import { CategoryEntity } from '../../entities/category.entity';
import { DepartmentEntity } from '../../entities/department.entity';

@AuthResolver(LayoutEntity)
export class LayoutsResolver {
    constructor(
        private readonly layoutsService: LayoutsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: LayoutEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @ResolveField(() => [CategoryEntity], { nullable: true })
    async categories(@Parent() parent: LayoutEntity): Promise<CategoryEntity[]> {
        return this.dataLoader.relationBatchOneMany(CategoryEntity, 'layout').load(parent.id);
    }

    @Query(() => LayoutsModel, { name: 'layouts_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<LayoutEntity>> {
        return this.layoutsService.search(body);
    }

    @Query(() => LayoutEntity, { name: 'layouts_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<LayoutEntity> {
        return this.layoutsService.findOne(id).then((res) => {
            if (!res) {
                throw new NotFoundException();
            }
            return res;
        });
    }

    @Mutation(() => LayoutEntity, { name: 'layouts_create' })
    async store(@Args('body') body: LayoutSaveInputDto, @AuthUser() auth: UserEntity): Promise<LayoutEntity> {
        return this.layoutsService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => LayoutEntity, { name: 'layouts_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: LayoutSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<LayoutEntity> {
        return this.layoutsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'layouts_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.layoutsService.softDelete(id, auth.id);
        return true;
    }
}
