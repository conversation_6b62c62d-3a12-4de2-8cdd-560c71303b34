import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IsUnique } from '../../../commons/validators/is-unique.validator';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { ItemStatus } from '../../../commons/enums.common';
import { LayoutEntity, LayoutType } from '../../../entities/layout.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { DepartmentEntity } from '../../../entities/department.entity';

@InputType()
@AutoSanitize()
export class LayoutSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @IsUnique(LayoutEntity)
    name: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    desc?: string;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    content: string;

    @Field(() => Int)
    @IsEnum(ItemStatus)
    status_id: ItemStatus;

    @Field(() => Int)
    @IsEnum(LayoutType)
    layout_type_id: LayoutType;

    @Field(() => Int)
    @IsNotEmpty()
    @IdExists(DepartmentEntity)
    department_id: number;
}
