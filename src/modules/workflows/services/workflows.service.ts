import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { WorkflowEntity } from '../../../entities/workflow.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class WorkflowsService extends BaseService<WorkflowEntity> {
    constructor(@InjectRepository(WorkflowEntity) public readonly repo: Repository<WorkflowEntity>) {
        super(repo);
    }
}
