import { WorkflowEntity } from '../../../entities/workflow.entity';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { WorkflowsService } from '../services/workflows.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { DepartmentEntity } from '../../../entities/department.entity';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { WorkflowsModel } from '../models/workflows.model';
import { NotFoundException } from '@nestjs/common';
import { WorkflowSaveInputDto } from '../dtos/workflow-save-input.dto';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';

@AuthResolver(WorkflowEntity)
export class WorkflowsResolver {
    constructor(
        private readonly workflowsService: WorkflowsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: WorkflowEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @Query(() => WorkflowsModel, { name: 'workflows_list' })
    async list(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<WorkflowEntity>> {
        return this.workflowsService.search(body);
    }

    @Query(() => WorkflowEntity, { name: 'workflows_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<WorkflowEntity> {
        return this.workflowsService.findOne(id).then((res) => {
            if (!res) {
                throw new NotFoundException();
            }
            return res;
        });
    }

    @Mutation(() => WorkflowEntity, { name: 'workflows_create' })
    async store(@Args('body') body: WorkflowSaveInputDto, @AuthUser() auth: UserEntity): Promise<WorkflowEntity> {
        return this.workflowsService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => WorkflowEntity, { name: 'workflows_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: WorkflowSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<WorkflowEntity> {
        return this.workflowsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'workflows_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<Boolean> {
        await this.workflowsService.softDelete(id, auth.id);
        return true;
    }
}
