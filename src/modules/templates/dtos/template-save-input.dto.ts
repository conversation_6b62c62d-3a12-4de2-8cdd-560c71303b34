import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IsUnique } from '../../../commons/validators/is-unique.validator';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { ItemStatus } from '../../../commons/enums.common';
import { TemplateEntity } from '../../../entities/template.entity';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { DepartmentEntity } from '../../../entities/department.entity';
import { FileEntity } from '../../../entities/file.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class TemplateSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @IsUnique(TemplateEntity)
    name: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    desc?: string;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    content: string;

    @Field(() => Int)
    @IsEnum(ItemStatus)
    status_id: ItemStatus;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(FileEntity)
    avatar_id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(DepartmentEntity)
    department_id: number;
}
