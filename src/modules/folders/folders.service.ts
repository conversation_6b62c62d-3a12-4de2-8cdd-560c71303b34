import { Injectable } from '@nestjs/common';
import { BaseService } from '../../commons/bases/base.service';
import { FolderEntity } from '../../entities/folder.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class FoldersService extends BaseService<FolderEntity> {
    constructor(@InjectRepository(FolderEntity) public readonly repo: Repository<FolderEntity>) {
        super(repo);
    }
}
