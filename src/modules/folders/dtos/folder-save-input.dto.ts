import { Field, InputType, Int } from '@nestjs/graphql';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { IsUnique } from '../../../commons/validators/is-unique.validator';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { FolderEntity } from '../../../entities/folder.entity';
import { DepartmentEntity } from '../../../entities/department.entity';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';

@InputType()
@AutoSanitize()
export class FolderSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @IsUnique(FolderEntity)
    name: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(FolderEntity)
    parent_id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(DepartmentEntity)
    department_id: number;
}
