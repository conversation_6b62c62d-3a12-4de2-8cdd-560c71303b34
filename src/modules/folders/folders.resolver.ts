import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { FoldersService } from './folders.service';
import { FolderEntity } from '../../entities/folder.entity';
import { FoldersModel } from './models/folders.model';
import { FolderSaveInputDto } from './dtos/folder-save-input.dto';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { IPaginatedType } from '../../commons/bases/base.model';
import { UserEntity } from '../../entities/user.entity';
import { DataLoaderService } from '../../data-loaders/data-loaders.service';
import { DepartmentEntity } from '../../entities/department.entity';
import { FileEntity } from '../../entities/file.entity';
import { AuthResolver } from '../../commons/decorators/graphql.decorators';
import { AuthUser } from '../auth/auth.decorator';

@AuthResolver(FolderEntity)
export class FoldersResolver {
    constructor(
        private readonly foldersService: FoldersService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: FolderEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @ResolveField(() => FolderEntity, { nullable: true })
    async parent(@Parent() parent: FolderEntity): Promise<FolderEntity | null> {
        if (!parent.parent_id) return null;
        return this.dataLoader.relationBatchOne(FolderEntity).load(parent.parent_id);
    }

    @ResolveField(() => [FolderEntity], { nullable: true })
    async children(@Parent() parent: FolderEntity): Promise<FolderEntity[]> {
        return this.dataLoader.relationBatchOneMany(FolderEntity, 'parent_id').load(parent.id);
    }

    @ResolveField(() => [FileEntity], { nullable: true })
    async files(@Parent() parent: FolderEntity): Promise<FileEntity[]> {
        return this.dataLoader.relationBatchOneMany(FileEntity, 'folder_id').load(parent.id);
    }

    @Query(() => FoldersModel, { name: 'folders_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<FolderEntity>> {
        return this.foldersService.search(body);
    }

    @Query(() => FolderEntity, { name: 'folders_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<FolderEntity> {
        return this.foldersService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => FolderEntity, { name: 'folders_create' })
    async store(@Args('body') body: FolderSaveInputDto, @AuthUser() auth: UserEntity): Promise<FolderEntity> {
        return this.foldersService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => FolderEntity, { name: 'folders_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: FolderSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<FolderEntity> {
        return this.foldersService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'folders_delete' })
    async delete(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.foldersService.softDelete(id, auth.id);
        return true;
    }
}
