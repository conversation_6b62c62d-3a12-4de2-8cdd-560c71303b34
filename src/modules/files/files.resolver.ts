import { Args, Query, Resolver } from '@nestjs/graphql';
import { FilesModel } from './models/files.model';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { IPaginatedType } from '../../commons/bases/base.model';
import { FilesService } from './files.service';
import { FileEntity } from '../../entities/file.entity';

@Resolver(() => FileEntity)
export class FilesResolver {
    constructor(private readonly filesService: FilesService) {}

    @Query(() => FilesModel, { name: 'files_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<FileEntity>> {
        return this.filesService.search(body);
    }
}
