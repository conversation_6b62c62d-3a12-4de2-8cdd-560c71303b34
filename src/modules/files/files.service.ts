import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { BaseService } from '../../commons/bases/base.service';
import { FileEntity } from '../../entities/file.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import slugify from 'slugify';
import appConf from '../../configs/app.conf';
import { convertUploadPath, normalizePath } from '../../commons/helpers/file.helper';
import { detectMimeType } from 'nodemailer/lib/mime-funcs';
import { UserEntity } from '../../entities/user.entity';
import { FileUploadInputDto } from './dtos/file-upload-input.dto';

@Injectable()
export class FilesService extends BaseService<FileEntity> {
    constructor(
        @InjectRepository(FileEntity)
        public readonly repo: Repository<FileEntity>
    ) {
        super(repo);
    }

    async insert(file: Express.Multer.File, auth: UserEntity, uploadData: FileUploadInputDto) {
        const f = await this.save({
            file_name: slugify(file.originalname),
            file_url: normalizePath(file.path),
            file_size: file.size,
            mime_type: detectMimeType(file.path),
            folder_id: uploadData.folder_id,
            department_id: uploadData.department_id,
            created_by: auth.id,
        });
        if (!f) throw new InternalServerErrorException();
        return {
            id: f.id,
            name: f.file_name,
            url: appConf.API_URL + convertUploadPath(file.path),
            folder_id: f.folder_id,
            department_id: f.department_id,
        };
    }
}
