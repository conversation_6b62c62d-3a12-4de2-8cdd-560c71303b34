import { Mo<PERSON><PERSON>, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { FilesService } from './files.service';
import { FilesController } from './files.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MulterModule } from '@nestjs/platform-express';
import { FileEntity } from '../../entities/file.entity';
import { multerConfig } from '../../configs/multer.conf';
import { FileSecurityMiddleware } from './file-security.middleware';
import { FilesResolver } from './files.resolver';

@Module({
    imports: [
        TypeOrmModule.forFeature([FileEntity]),
        MulterModule.register({
            dest: multerConfig.dest,
        }),
    ],
    providers: [FilesService, FileSecurityMiddleware, FilesResolver],
    controllers: [FilesController],
})
export class FilesModule {
    configure(consumer: MiddlewareConsumer) {
        consumer.apply(FileSecurityMiddleware).forRoutes({ path: '*', method: RequestMethod.POST });
    }
}
