import { IsNotEmpty, IsOptional } from 'class-validator';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { DepartmentEntity } from '../../../entities/department.entity';
import { FolderEntity } from '../../../entities/folder.entity';

export class FileUploadInputDto {
    @IsNotEmpty()
    @IdExists(DepartmentEntity)
    department_id: number;

    @IsOptional()
    @IdExists(FolderEntity)
    folder_id?: number;
}
