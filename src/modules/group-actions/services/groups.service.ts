import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { GroupEntity } from '../../../entities/group.entity';
import { BaseService } from '../../../commons/bases/base.service';
import { ActionEntity } from '../../../entities/action.entity';
import { IGroupSaveInput } from '../dtos/group-save-input.dto';
import { ISaveActions } from '../dtos/save-actions.dto';

@Injectable()
export class GroupsService extends BaseService<GroupEntity> {
    constructor(
        @InjectRepository(GroupEntity)
        public readonly repo: Repository<GroupEntity>
    ) {
        super(repo);
    }

    async saveGroup(body: IGroupSaveInput, id?: number): Promise<GroupEntity> {
        let group: GroupEntity = new GroupEntity();
        if (id) {
            group = (await this.findOne(id)) as GroupEntity;
            if (!group) throw new NotFoundException();
        }
        Object.assign(group, body);
        if (body.action_ids)
            group.actions = await this.repo.manager.find(ActionEntity, { where: { id: In(body.action_ids) } });
        return this.repo.manager.transaction(async (run) => {
            return run.save(group);
        });
    }

    async saveActions(body: ISaveActions) {
        const group = await this.findOne(body.group_id);
        if (!group) throw new NotFoundException();
        group.actions = await this.repo.manager.find(ActionEntity, { where: { id: In(body.action_ids) } });
        await this.repo.manager.transaction(async (run) => {
            await run.save(group);
        });
    }
}
