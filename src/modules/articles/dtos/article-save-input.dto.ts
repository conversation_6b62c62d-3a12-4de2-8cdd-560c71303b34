import { Field, GraphQLISODateTime, InputType, Int, ObjectType } from '@nestjs/graphql';
import { IsArray, IsBoolean, IsDate, IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { ArticleEntity } from '../../../entities/article.entity';
import { ArticleTypes } from '../../../entities/workflow-permission-article-type.entity';
import { ArticleKind } from '../../../entities/article-article-kind.entity';
import { FileEntity } from '../../../entities/file.entity';
import { WorkflowEntity } from '../../../entities/workflow.entity';
import { PseudonymsEntity } from '../../../entities/pseudonyms.entity';
import { LayoutEntity } from '../../../entities/layout.entity';
import { DepartmentEntity } from '../../../entities/department.entity';
import { TemplateEntity } from '../../../entities/template.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { Type } from 'class-transformer';

@InputType()
@AutoSanitize()
export class ArticleSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    title: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    sub_title?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    brief_title?: string;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    slug: string;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    desc: string;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    content: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(FileEntity)
    avatar1_id?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(FileEntity)
    avatar2_id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(WorkflowEntity)
    workflow_id: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(ArticleTypes)
    article_type_id: ArticleTypes;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(PseudonymsEntity)
    pseudonym_id?: number;

    @IsString()
    @Field(() => String, { nullable: true })
    @IsOptional()
    pseudonym_name?: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(LayoutEntity)
    layout_id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(DepartmentEntity)
    department_id: number;

    @Field(() => GraphQLISODateTime, { nullable: true })
    @IsOptional()
    @IsDate()
    @Type(() => Date)
    publish_date?: Date;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    source?: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(FileEntity)
    file_id?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(TemplateEntity)
    template_id?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(ArticleEntity)
    root_article_id?: number;

    @Field(() => [Int], { nullable: true })
    @IsOptional()
    @IsArray()
    @IsEnum(ArticleKind, { each: true })
    article_kind_ids?: ArticleKind[];

    @Field(() => [String], { nullable: true })
    @IsOptional()
    article_tags?: string[];

    @Field(() => [String], { nullable: true })
    @IsOptional()
    new_article_notes?: string[];

    @Field(() => [ArticleCategoryInput], { nullable: true })
    @IsOptional()
    article_categories?: ArticleCategoryInput[];

    @Field(() => [Int], { nullable: true })
    @IsOptional()
    @IsArray()
    related_article_ids?: number[];

    @Field(() => [Int], { nullable: true })
    @IsOptional()
    @IsArray()
    article_type_copy_ids?: number[];

    @Field(() => Boolean, { nullable: true })
    @IsOptional()
    @IsBoolean()
    is_sync?: boolean;
}

@InputType()
@ObjectType()
export class ArticleCategoryInput {
    @Field(() => Int)
    category_id: number;

    @Field(() => Int)
    display_order: number;
}
