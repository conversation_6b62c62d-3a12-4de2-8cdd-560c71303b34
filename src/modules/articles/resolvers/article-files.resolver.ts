import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { ArticleFileEntity } from '../../../entities/article-file.entity';
import { ArticleFilesService } from '../services/article-files.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { ArticleFilesModel } from '../models/article-files.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { NotFoundException } from '@nestjs/common';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { ArticleFileSaveInputDto } from '../dtos/article-file-save-input.dto';
import { ArticleEntity } from '../../../entities/article.entity';
import { FileEntity } from '../../../entities/file.entity';

@AuthResolver(ArticleFileEntity)
export class ArticleFilesResolver {
    constructor(
        private readonly articleFilesService: ArticleFilesService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => ArticleEntity, { nullable: true })
    async article(@Parent() parent: ArticleFileEntity): Promise<ArticleEntity | null> {
        return this.dataLoader.relationBatchOne(ArticleEntity).load(parent.article_id);
    }

    @ResolveField(() => FileEntity, { nullable: true })
    async file(@Parent() parent: ArticleFileEntity): Promise<FileEntity | null> {
        if (!parent.file_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(parent.file_id);
    }

    @Query(() => ArticleFilesModel, { name: 'article_files_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<ArticleFileEntity>> {
        return this.articleFilesService.search(body);
    }

    @Query(() => ArticleFileEntity, { name: 'article_files_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<ArticleFileEntity> {
        return this.articleFilesService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => ArticleFileEntity, { name: 'article_files_create' })
    async store(@Args('body') body: ArticleFileSaveInputDto, @AuthUser() auth: UserEntity): Promise<ArticleFileEntity> {
        return this.articleFilesService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => ArticleFileEntity, { name: 'article_files_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ArticleFileSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleFileEntity> {
        return this.articleFilesService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'article_files_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.articleFilesService.softDelete(id, auth.id);
        return true;
    }
}
