import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleCategoryEntity } from '../../../entities/article-category.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class ArticleCategoriesService extends BaseService<ArticleCategoryEntity> {
    constructor(@InjectRepository(ArticleCategoryEntity) public readonly repo: Repository<ArticleCategoryEntity>) {
        super(repo);
    }
}
