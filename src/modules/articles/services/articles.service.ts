import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleEntity } from '../../../entities/article.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ry<PERSON><PERSON><PERSON>, Repository } from 'typeorm';
import { ArticleSaveInputDto } from '../dtos/article-save-input.dto';
import { ArticleArticleKindEntity } from '../../../entities/article-article-kind.entity';
import { ArticleTagEntity } from 'src/entities/article-tag.entity';
import { TagEntity } from 'src/entities/tag.entity';
import { BusinessException } from 'src/commons/exceptions/business.exception';
import { PseudonymsEntity } from 'src/entities/pseudonyms.entity';
import { ItemStatus } from 'src/commons/enums.common';
import slugify from 'slugify';
import { ArticleNoteEntity } from 'src/entities/article-note.entity';
import { ArticleCategoryEntity } from 'src/entities/article-category.entity';
import { RelatedArticleEntity } from 'src/entities/related-article.entity';

@Injectable()
export class ArticlesService extends BaseService<ArticleEntity> {
    constructor(
        @InjectRepository(ArticleEntity) public readonly repo: Repository<ArticleEntity>,
        @InjectRepository(ArticleArticleKindEntity)
        private readonly articleKindRepo: Repository<ArticleArticleKindEntity>,
        private dataSource: DataSource
    ) {
        super(repo);
    }

    async saveArticle(
        data: ArticleSaveInputDto,
        userId: number,
        queryRunnerProps?: QueryRunner
    ): Promise<ArticleEntity> {
        const { article_kind_ids, ...articleData } = data;

        // Extract only the fields that are actually saved in the database
        const validFields = [
            'title',
            'sub_title',
            'brief_title',
            'slug',
            'desc',
            'content',
            'avatar1_id',
            'avatar2_id',
            'workflow_id',
            'article_type_id',
            'language_id',
            'pseudonym_id',
            'layout_id',
            'department_id',
            'publish_date',
            'source',
            'file_id',
            'template_id',
            'root_article_id',
            'lock_user_id',
            'lock_at',
        ];
        const filteredArticleData: Record<string, any> = Object.keys(articleData)
            .filter((key) => validFields.includes(key))
            .reduce((obj, key) => {
                obj[key] = articleData[key];
                return obj;
            }, {});

        const queryRunner = queryRunnerProps || this.dataSource.createQueryRunner();

        // Only start transaction if this is the root call (no queryRunnerProps)
        if (!queryRunnerProps) {
            await queryRunner.connect();
            await queryRunner.startTransaction();
        }

        try {
            let article: ArticleEntity = new ArticleEntity();

            // Pseudonym handling
            if (!!data.pseudonym_id) {
                const pseudonym = await queryRunner.manager.findOne(PseudonymsEntity, {
                    where: { id: data.pseudonym_id },
                });
                if (!pseudonym) {
                    throw new BusinessException('Pseudonym not found');
                }
                filteredArticleData.pseudonym_id = data.pseudonym_id;
            } else if (!!data.pseudonym_name) {
                const pseudonym = await queryRunner.manager.findOne(PseudonymsEntity, {
                    where: { name: data.pseudonym_name },
                });

                if (!pseudonym) {
                    filteredArticleData.pseudonym_id = data.pseudonym_id;
                } else {
                    const pseudonymInsertResult: InsertResult = await queryRunner.manager.insert(PseudonymsEntity, {
                        name: data.pseudonym_name,
                        status_id: ItemStatus.ACTIVE,
                        is_default: false,
                        user_id: userId,
                    });
                    filteredArticleData.pseudonym_id = pseudonymInsertResult.raw[0].id;
                }
            } else {
                throw new BusinessException('Pseudonym id or Pseudonym name is required');
            }

            // Article creation or update
            if (data.id) {
                article = await queryRunner.manager.findOneOrFail(ArticleEntity, {
                    where: { id: data.id },
                });
                filteredArticleData.updated_by = userId;
                filteredArticleData.updated_at = new Date();
                Object.assign(article, filteredArticleData);
            } else {
                article = Object.assign(new ArticleEntity(), filteredArticleData);
                article.created_by = userId;
                const res = await queryRunner.manager.insert(ArticleEntity, article);
                article = await queryRunner.manager.findOneOrFail(ArticleEntity, {
                    where: { id: res.identifiers[0].id },
                });
            }

            // Related articles handling
            if (data.related_article_ids && data.related_article_ids.length > 0) {
                await queryRunner.manager.delete(RelatedArticleEntity, {
                    article_id: article.id,
                });

                const relatedArticles = data.related_article_ids.map((relatedArticleId) => ({
                    article_id: article.id,
                    related_article_id: relatedArticleId,
                }));

                await queryRunner.manager.insert(RelatedArticleEntity, relatedArticles);
            }

            // Kinds handling
            if (article_kind_ids && article_kind_ids.length > 0) {
                await queryRunner.manager.delete(ArticleArticleKindEntity, {
                    article_id: article.id,
                });

                const articleKinds = article_kind_ids.map((kindId) => ({
                    article_id: article.id,
                    article_kind_id: kindId,
                }));

                await queryRunner.manager.insert(ArticleArticleKindEntity, articleKinds);
            }

            // Tags handling
            if (data.article_tags) {
                await queryRunner.manager.delete(ArticleTagEntity, {
                    article_id: article.id,
                });

                const tags = await queryRunner.manager.find(TagEntity, {
                    where: {
                        name: In(data.article_tags),
                        department_id: data.department_id,
                    },
                });

                const tagNeedToCreate = data.article_tags.filter((tag) => !tags.some((t) => t.name === tag));

                const tagEntities = tagNeedToCreate.map((tag) => ({
                    name: tag,
                    department_id: data.department_id,
                    slug: slugify(tag, { lower: true }),
                    status_id: ItemStatus.ACTIVE,
                    created_by: userId,
                    created_at: new Date(),
                }));
                const tagInsertResult: InsertResult = await queryRunner.manager.insert(TagEntity, tagEntities);

                const articleTagsNew = tagInsertResult.identifiers.map((tag) => ({
                    article_id: article.id,
                    tag_id: tag.id,
                    created_by: userId,
                    created_at: new Date(),
                }));

                const articleTagsOld = tags.map((tag) => ({
                    article_id: article.id,
                    tag_id: tag.id,
                    created_by: userId,
                    created_at: new Date(),
                }));
                const articleTags = [...articleTagsNew, ...articleTagsOld];

                await queryRunner.manager.insert(ArticleTagEntity, articleTags);
            }

            // Insert more notes
            if (data.new_article_notes && data.new_article_notes.length > 0) {
                const newNotes = data.new_article_notes.map((note) => ({
                    article_id: article.id,
                    content: note,
                    created_by: userId,
                    created_at: new Date(),
                }));

                await queryRunner.manager.insert(ArticleNoteEntity, newNotes);
            }

            // Delete old categories & insert new categories
            if (data.article_categories && data.article_categories.length > 0) {
                await queryRunner.manager.delete(ArticleCategoryEntity, {
                    article_id: article.id,
                });

                const newCategories = data.article_categories.map((category) => ({
                    article_id: article.id,
                    category_id: category.category_id,
                    display_order: category.display_order,
                    created_by: userId,
                    created_at: new Date(),
                }));

                await queryRunner.manager.insert(ArticleCategoryEntity, newCategories);
            }

            if (data.article_type_copy_ids && data.article_type_copy_ids.length > 0) {
                const dataClone = { ...data };
                const { article_type_copy_ids, is_sync, ...rest } = dataClone;

                for (const element of data.article_type_copy_ids) {
                    await this.saveArticle(
                        {
                            ...rest,
                            article_type_id: element,
                            article_type_copy_ids: undefined,
                            root_article_id: article.id,
                        },
                        userId,
                        queryRunner
                    );
                }
            }

            await queryRunner.manager.update(ArticleEntity, article.id, article);

            if (!queryRunnerProps) {
                await queryRunner.commitTransaction();
            }

            return article;
        } catch (error) {
            if (!queryRunnerProps) {
                await queryRunner.rollbackTransaction();
            }
            throw error;
        } finally {
            if (!queryRunnerProps) {
                await queryRunner.release();
            }
        }
    }
}
