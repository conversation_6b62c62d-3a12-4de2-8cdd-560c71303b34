import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { ConfigEntity } from '../../../entities/config.entity';
import { ConfigsService } from '../services/configs.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { DepartmentEntity } from '../../../entities/department.entity';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { ConfigsModel } from '../models/configs.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { NotFoundException } from '@nestjs/common';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { ConfigSaveInputDto } from '../dtos/config-save-input.dto';

@AuthResolver(ConfigEntity)
export class ConfigsResolver {
    constructor(
        private readonly configsService: ConfigsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: ConfigEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @Query(() => ConfigsModel, { name: 'configs_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<ConfigEntity>> {
        return this.configsService.search(body);
    }

    @Query(() => ConfigEntity, { name: 'configs_detail' })
    async view(@Args('id', { type: () => Number }) id: number): Promise<ConfigEntity> {
        return this.configsService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => ConfigEntity, { name: 'configs_create' })
    async store(@Args('body') body: ConfigSaveInputDto, @AuthUser() auth: UserEntity): Promise<ConfigEntity> {
        return this.configsService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => ConfigEntity, { name: 'configs_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ConfigSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ConfigEntity> {
        return this.configsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'configs_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<Boolean> {
        await this.configsService.softDelete(id, auth.id);
        return true;
    }
}
