import { Field, InputType, Int } from '@nestjs/graphql';
import { IsNotEmpty, IsString } from 'class-validator';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { DepartmentEntity } from '../../../entities/department.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { ConfigEntity } from '../../../entities/config.entity';
import { IsUniqueBy } from '../../../commons/validators/is-unique-by.validator';

@InputType()
@AutoSanitize()
export class ConfigSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @IsUniqueBy(ConfigEntity, 'department_id', { message: '<PERSON>ã cấu hình đã tồn tại' })
    code: string;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    name: string;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    content: string;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(DepartmentEntity)
    department_id: number;
}
