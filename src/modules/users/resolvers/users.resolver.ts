import { Args, Int, Mu<PERSON>, Parent, Query, ResolveField } from '@nestjs/graphql';
import { UsersService } from '../services/users.service';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { UsersModel } from '../models/users.model';
import { UserEntity, UserRoles } from '../../../entities/user.entity';
import { NotFoundException } from '@nestjs/common';
import { UserCreateInputDto } from '../dtos/user-create-input.dto';
import { UserUpdateInputDto } from '../dtos/user-update-input.dto';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { AuthUser } from '../../auth/auth.decorator';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { ResetPasswordInputDto } from '../dtos/reset-password-input.dto';
import { Roles } from '../../../commons/decorators/roles.decorator';
import { ChangeGroupsInputDto } from '../dtos/change-groups-input.dto';
import { FileEntity } from '../../../entities/file.entity';
import { GroupEntity } from '../../../entities/group.entity';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { PseudonymsEntity } from '../../../entities/pseudonyms.entity';
import { UserDepartmentEntity } from 'src/entities/user-department.entity';
import { ChangeDepartmentsInputDto } from '../dtos/change-departments-input.dto';

@AuthResolver(UserEntity)
@Roles(UserRoles.ADMIN)
export class UsersResolver {
    constructor(
        private readonly usersService: UsersService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => FileEntity, { nullable: true })
    async avatar(@Parent() user: UserEntity): Promise<FileEntity | null> {
        if (!user.avatar_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(user.avatar_id);
    }

    @ResolveField(() => [GroupEntity], { nullable: true })
    async groups(@Parent() user: UserEntity): Promise<GroupEntity[]> {
        return this.dataLoader.relationBatchManyMany(GroupEntity, 'users').load(user.id);
    }

    @ResolveField(() => [UserDepartmentEntity], { nullable: true })
    async userDepartments(@Parent() user: UserEntity): Promise<UserDepartmentEntity[]> {
        return this.dataLoader.relationBatchOneMany(UserDepartmentEntity, 'user').load(user.id);
    }

    @ResolveField(() => [PseudonymsEntity], { nullable: true })
    async pseudonyms(@Parent() user: UserEntity): Promise<PseudonymsEntity[]> {
        return this.dataLoader.relationBatchOneMany(PseudonymsEntity, 'user').load(user.id);
    }

    @Query(() => UsersModel, { name: 'users_list' })
    async users(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<UserEntity>> {
        return this.usersService.search(body);
    }

    @Query(() => UserEntity, { name: 'users_detail' })
    async view(@Args('id', { type: () => Int }) id: number) {
        return this.usersService.findOne(id).then((res) => {
            if (!res) {
                throw new NotFoundException();
            }
            return res;
        });
    }

    @Mutation(() => UserEntity, { name: 'users_create' })
    async store(@Args('body') body: UserCreateInputDto, @AuthUser() auth: UserEntity): Promise<UserEntity> {
        return this.usersService.store(body, auth);
    }

    @Mutation(() => UserEntity, { name: 'users_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: UserUpdateInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<UserEntity> {
        body['contextId'] = id;
        return this.usersService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'users_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number): Promise<Boolean> {
        await this.usersService.delete(id);
        return true;
    }

    @Mutation(() => Boolean, { name: 'users_reset_pass' })
    async resetPass(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ResetPasswordInputDto
    ): Promise<Boolean> {
        await this.usersService.resetPassword(id, body);
        return true;
    }

    @Mutation(() => Boolean, { name: 'users_change_groups' })
    async changeGroups(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ChangeGroupsInputDto
    ): Promise<Boolean> {
        await this.usersService.changeGroups(id, body);
        return true;
    }

    @Mutation(() => Boolean, { name: 'users_change_departments' })
    async changeDepartments(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ChangeDepartmentsInputDto
    ): Promise<Boolean> {
        await this.usersService.changeDepartments(id, body);
        return true;
    }
}
