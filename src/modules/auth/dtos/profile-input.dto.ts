import { IsBoolean, IsEmail, IsEnum, IsISO8601, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Field, InputType, Int } from '@nestjs/graphql';
import { Genders, UserEntity } from '../../../entities/user.entity';
import { userValidationMessages, validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { IsUnique } from '../../../commons/validators/is-unique.validator';
import { FileEntity } from '../../../entities/file.entity';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { AutoSanitize, SanitizeEmail } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize() // Tự động áp dụng SanitizeBasic cho các field không có decorator sanitize
export class ProfileInputDto extends BaseUpdateInputDto {
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    full_name?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsNotEmpty({ message: userValidationMessages.REQUIRED_EMAIL })
    @IsString()
    @IsUnique(UserEntity)
    phone?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsNotEmpty({ message: userValidationMessages.REQUIRED_EMAIL })
    @IsEmail({}, { message: userValidationMessages.INVALID_EMAIL })
    @IsString()
    @IsUnique(UserEntity)
    @SanitizeEmail() // Sanitize email: trim, normalizeEmail, toLowerCase
    email?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    address?: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(FileEntity)
    avatar_id?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsEnum(Genders)
    gender_id?: Genders;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    telegram_id?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @IsISO8601({})
    birthday?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    email_notify?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    telegram_notify?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    zalo_notify?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    email_verified?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    phone_verified?: boolean;
}
