import { Global, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { JwtStrategy } from './jwt.strategy';
import { AuthResolver } from './auth.resolver';
import { AuthService } from './services/auth.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UserEntity } from '../../entities/user.entity';
import { PersonalTokenEntity } from '../../entities/personal-token.entity';
import { PersonalTokenService } from './services/personal-token.service';
import { IdExistsConstraint } from '../../commons/validators/id-exists.validator';
import { JwtAuthGuard } from './jwt-auth.guard';
import { UniqueEmailByRoleConstraint } from '../../commons/validators/unique-email.validator';
import { UniquePhoneNumberByRoleConstraint } from '../../commons/validators/unique-phone_number.validator';
import { IsUniqueConstraint } from '../../commons/validators/is-unique.validator';
import { IsSureUniqueConstraint } from '../../commons/validators/is-sure-unique.validator';
import { IsUniqueByConstraint } from '../../commons/validators/is-unique-by.validator';
import { LoginLogsModule } from '../login-logs/login-logs.module';

@Global()
@Module({
    imports: [
        TypeOrmModule.forFeature([UserEntity, PersonalTokenEntity]),
        PassportModule.register({ defaultStrategy: 'jwt' }),
        JwtModule.registerAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => {
                const secret = configService.get<string>('JWT_SECRET');
                if (!secret) {
                    throw new Error('JWT_SECRET is not defined in environment variables');
                }
                return {
                    secret,
                    signOptions: { expiresIn: '1d' },
                };
            },
        }),
        LoginLogsModule,
    ],
    providers: [
        JwtStrategy,
        JwtAuthGuard,
        AuthService,
        AuthResolver,
        PersonalTokenService,
        IdExistsConstraint,
        UniqueEmailByRoleConstraint,
        UniquePhoneNumberByRoleConstraint,
        IsUniqueConstraint,
        IsSureUniqueConstraint,
        IsUniqueByConstraint,
    ],
    exports: [
        JwtStrategy,
        JwtAuthGuard,
        JwtModule,
        AuthService,
        PersonalTokenService,
        IdExistsConstraint,
        UniqueEmailByRoleConstraint,
        UniquePhoneNumberByRoleConstraint,
        IsUniqueConstraint,
        IsSureUniqueConstraint,
    ],
})
export class AuthModule {}
