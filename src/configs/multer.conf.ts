import { diskStorage, FileFilterCallback, Options } from 'multer';
import { mkdir } from '../commons/helpers/common.helper';
import { BadRequestException } from '@nestjs/common';
import { Request } from 'express';
import { IMAGES_PATH, OTHERS_PATH, UPLOAD_DIR } from '../commons/helpers/file.helper';
import appConf from './app.conf';
import slugify from 'slugify';

export const multerConfig = {
    dest: './' + UPLOAD_DIR, // Thư mục lưu file
    limitSize: appConf.LIMIT_UPLOAD_SIZE * 1024 * 1024, // Giới hạn dung lượng file upload theo MB
    maxFiles: appConf.MAX_FILES_PER_UPLOAD, // Giới hạn số lượng file upload trong một request
    allowedFileTypes: appConf.ALLOWED_FILE_TYPES, // Các loại file được phép
    allowedMimeTypes: appConf.ALLOWED_MIME_TYPES, // Các loại MIME được phép
};

export const multerOptions: Options = {
    storage: diskStorage({
        destination: (_, file, cb) => {
            let subPath: String;
            //Chia subPath theo mimetype
            if (file.mimetype.match(/\/(jpg|jpeg|png|gif|webp|bmp)$/)) {
                subPath = `/${IMAGES_PATH}`;
            } else {
                subPath = `/${OTHERS_PATH}`;
            }
            // Chia subPath theo ngày
            subPath += `/${new Date().toISOString().slice(0, 10)}`;
            // Tạo thư mục nếu chưa tồn tại
            let uploadPath = multerConfig.dest + subPath;
            mkdir(uploadPath);

            cb(null, uploadPath); // Lưu file vào thư mục chỉ định
        },
        filename: (_, file, cb) => {
            const randomName = Date.now() + '-' + Math.round(Math.random() * 1e9);
            cb(null, `${randomName}_${slugify(file.originalname)}`);
        },
    }),
    fileFilter: (_: Request, file: Express.Multer.File, cb: FileFilterCallback) => {
        // Kiểm tra mimetype trước
        const allowedTypes = multerConfig.allowedFileTypes.join('|');
        const fileTypeRegex = new RegExp(`\\/(${allowedTypes})$`);

        if (!file.mimetype.match(fileTypeRegex)) {
            return cb(
                new BadRequestException(
                    `Invalid file type! Only ${multerConfig.allowedFileTypes.join(', ')} are allowed.`
                ) as any,
                false
            );
        }

        // Chấp nhận file dựa trên mimetype, kiểm tra nội dung sẽ được thực hiện trong middleware
        cb(null, true);
    },
    limits: {
        fileSize: multerConfig.limitSize,
        files: multerConfig.maxFiles, // Giới hạn số lượng file
    },
};
