NODE_ENV=development

#Database
PG_HOST=localhost
PG_PORT=5432
PG_USER=postgres
PG_PASS=postgres
PG_DB=mediasoft_api

#App
CORS_ORIGIN=*
APP_PORT=8003
AT_SECRET=
AT_TIMEOUT=24h
RT_SECRET=
RT_TIMEOUT=30d
JWT_SECRET=
LIMIT_SEND_FORGOT_PASS=5
OTP_EXPIRATION_TIME=1#hours
PAGE_DEFAULT=20
LIMIT_UPLOAD_SIZE=5#MB
MAX_FILES_PER_UPLOAD=5
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,webp,bmp
ALLOWED_MIME_TYPES=image/jpeg,image/png,image/gif,image/webp,image/bmp
API_URL=http://localhost:8003/

#Mail
MAIL_HOST=
MAIL_PORT=
MAIL_USER=
MAIL_PASSWORD=
MAIL_FROM=
SUPPORT_MAIL=